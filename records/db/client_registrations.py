from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models


def default_registration():
    return models.ClientRegistration(
        is_primary=True,
        registered_date=None,
        state_of_incorporation='',
        billed_to='',
        last_soi_filed=None,
        state_entity='',
        notes='',
    )


@base.session_aware()
async def create_client_registration(values, session=None):
    client_registration = models.ClientRegistration(**values)

    try:
        session.add(client_registration)
        await session.flush()
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for ClientRegistration: %s" % e
        )

    return client_registration


@base.session_aware()
async def update_client_registration(client_registration: models.ClientRegistration, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientRegistration).where(models.ClientRegistration.id == client_registration.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_registration.update(new_values)

    return client_registration


@base.session_aware()
async def delete_client_registration(id: int, session=None):
    delete_q = delete(models.ClientRegistration).where(models.ClientRegistration.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_registrations(client_id: str, session=None):
    delete_q = delete(models.ClientRegistration).where(models.ClientRegistration.client_id == client_id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_registrations(ids: list[int] = None, client_id=None, session=None):
    delete_q = delete(models.ClientRegistration)
    if not ids and not client_id:
        raise ValueError('Either ids or client_id must be provided')
    if ids:
        delete_q = delete_q.where(models.ClientRegistration.id.in_(ids))
    if client_id:
        delete_q = delete_q.where(models.ClientRegistration.client_id == client_id)
    await session.execute(delete_q)


@base.session_aware()
async def list_client_registrations(
    client_id: str,
    all: bool = False,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    offset = None
    # if limit and page:
    #     limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ClientRegistration, models.RegAgent).where(models.ClientRegistration.client_id == client_id)
    query = query.outerjoin(models.RegAgent, models.ClientRegistration.registered_agent_id == models.RegAgent.id)
    if not all:
        query = query.where(models.ClientRegistration.is_primary == False)
    # if q:
    #     query = query.where(models.ClientRegistration.name.ilike(f'%{q}%'))

    if not hasattr(models.ClientRegistration, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.ClientRegistration, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    joined_res = res.fetchall()
    regs = []
    for reg, agent in joined_res:
        reg.registered_agent = agent
        regs.append(reg)

    return regs


@base.session_aware()
async def list_client_registrations_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ClientRegistration)
    if ids:
        query = query.where(models.ClientRegistration.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_client_registration_by_id(id: int, session=None):
    return await base.get_by_id(models.ClientRegistration, id, session=session)


@base.session_aware()
async def get_primary_client_registration(client_id: str, session=None):
    query = select(models.ClientRegistration, models.RegAgent).where(models.ClientRegistration.client_id == client_id)
    query = query.where(models.ClientRegistration.is_primary == True)

    # join reg agent
    query = query.outerjoin(models.RegAgent, models.ClientRegistration.registered_agent_id == models.RegAgent.id)

    res = await session.execute(query)

    res = res.fetchall()
    if res:
        reg, agent = res[0][0], res[0][1]
        reg.registered_agent = agent

        return reg
    return None


@base.session_aware()
async def get_client_registration_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    # if q:
    #     q = f'%{q}%'
    #     where.append('name ILIKE :q')
    #     params['q'] = q

    raw = 'SELECT count(*) AS count FROM client_registrations'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
