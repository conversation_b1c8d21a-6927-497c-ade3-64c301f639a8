import datetime
import os
from typing import Optional, List

from fastapi import APIRouter, Depends, HTTPException
from fastapi import Response
from fastapi.responses import ORJSONResponse
from pydantic import BaseModel, ValidationError, field_validator

from records.api import addresses
from records.api import auth_utils, search
from records.api import authorized_signers as authorized_signers_api
from records.api import bank_accounts as bank_accounts_api
from records.api import changes_pending
from records.api import client_addresses
from records.api import client_contacts as contact_api
from records.api import client_persons
from records.api import client_registrations
from records.api import client_services
from records.api import client_tasks
from records.api import files
from records.api import questions
from records.api import reg_agents
from records.context import context as ctx
from records.db import api as db_api, base
from records.db import models
from records.services import clients, schemas
from records.utils import json_utils


class ClientBase(BaseModel):
    accounting_method: Optional[str] = None
    active_since: Optional[datetime.datetime] = None
    agr_signed: Optional[datetime.datetime] = None
    agreement_sum: Optional[float] = None
    billing_method: Optional[str] = None
    bookkeeping: Optional[bool] = None
    company_phone: Optional[str] = None
    control_by: Optional[str] = None
    cpa: Optional[str] = None
    description: Optional[str] = None
    dissolution_date: Optional[datetime.datetime] = None
    ein: Optional[str] = None
    fedtaxforms: Optional[str] = None
    financial_year_end: Optional[str] = None
    financial_year_end_for_subsidiary: Optional[str] = None
    incorp_by: Optional[str] = None
    legal_ent_type: Optional[str] = None
    login: Optional[str] = None
    monthly_bill: Optional[float] = None
    naicscode: Optional[str] = None
    notes_accounting: Optional[str] = None
    notes_address: Optional[str] = None
    notes_agreement: Optional[str] = None
    notes_contacts: Optional[str] = None
    notes_main: Optional[str] = None
    notes_shareholders: Optional[str] = None
    optional_share_count: Optional[int] = None
    paid_by: Optional[str] = None
    paid_by_mail: Optional[str] = None
    password: Optional[str] = None
    payroll: Optional[bool] = None
    renewal_date: Optional[datetime.datetime] = None
    renewal_date_mail: Optional[datetime.datetime] = None
    since: Optional[datetime.datetime] = None
    statetaxforms: Optional[str] = None
    status: Optional[str] = None
    subjurisd: Optional[str] = None
    subsidiary_legal_entity_type: Optional[str] = None
    subsidiary_to_consolidate: Optional[str] = None
    total_shares: Optional[int] = None
    withdrawal_date: Optional[datetime.datetime] = None

    # connections ids
    source_id: Optional[str] = None
    manager_id: Optional[str] = None

    _date_validator = field_validator(
        *['active_since', 'agr_signed', 'dissolution_date', 'renewal_date',
          'renewal_date_mail', 'since', 'withdrawal_date'],
        mode='before'
    )(json_utils.date_validator)


class ClientManager(BaseModel):
    id: Optional[str] = None
    user_id: Optional[int] = None
    title: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    role_name: Optional[str] = None


class ClientAddress(BaseModel):
    id: Optional[str] = None
    address_type: Optional[str] = None
    renewal_date: Optional[datetime.datetime] = None
    phone: Optional[str] = None
    paid_by: Optional[str] = None
    note: Optional[str] = None
    address_id: Optional[str] = None
    address: Optional[addresses.AddressCreate] = None

    _date_validator = field_validator(
        *['renewal_date'],
        mode='before'
    )(json_utils.date_validator)


class ClientAuthorizedSigner(BaseModel):
    id: Optional[int] = None
    client_person_id: Optional[str] = None
    note: Optional[str] = None
    person: Optional[client_persons.PersonCreate] = None


class Service(BaseModel):
    id: Optional[str] = None
    title: Optional[str] = None
    price: Optional[float] = None
    price_type: Optional[str] = None


class ClientService(BaseModel):
    id: Optional[int] = None
    service_id: Optional[str] = None
    service: Optional[Service] = None
    active_since: Optional[datetime.datetime] = None
    active_until: Optional[datetime.datetime] = None
    note: Optional[str] = None
    discount_percent: Optional[int] = None
    discount_amount: Optional[str] = None
    total: Optional[str] = None

    _date_validator = field_validator(
        *['active_since', 'active_until'],
        mode='before'
    )(json_utils.date_validator)


class ClientTask(BaseModel):
    id: Optional[int] = None
    date: Optional[datetime.datetime] = None
    task: Optional[str] = None
    due_date: Optional[datetime.datetime] = None
    status: Optional[str] = None
    manager_id: Optional[str] = None
    manager: Optional[ClientManager] = None

    _date_validator = field_validator(
        *['date', 'due_date'],
        mode='before'
    )(json_utils.date_validator)


class ClientTaxReporting(BaseModel):
    id: Optional[int] = None
    year: Optional[str] = None
    reporting_1099: Optional[str] = None
    tax_return_by: Optional[str] = None
    note: Optional[str] = None
    files: Optional[str] = None


class ClientShareholder(BaseModel):
    id: Optional[str] = None
    ownership: Optional[str] = None
    position: Optional[str] = None
    note: Optional[str] = None
    client_person_id: Optional[str] = None
    person: Optional[client_persons.PersonCreate] = None


class ClientRegistration(BaseModel):
    id: Optional[int] = None
    registered_agent_id: Optional[str] = None
    state_of_incorporation: Optional[str] = None
    registered_date: Optional[datetime.datetime] = None
    terminated_date: Optional[datetime.datetime] = None
    annual_compliance_due_date: Optional[datetime.datetime] = None
    billed_to: Optional[str] = None
    last_soi_filed: Optional[datetime.datetime] = None
    state_entity: Optional[str] = None
    notes: Optional[str] = None
    registered_agent: Optional[reg_agents.RegAgentCreate] = None

    _date_validator = field_validator(
        *['registered_date', 'terminated_date', 'annual_compliance_due_date', 'last_soi_filed'],
        mode='before'
    )(json_utils.date_validator)


class ClientShareClass(BaseModel):
    id: Optional[int] = None
    stock_authorized: Optional[int] = None
    stock_issued: Optional[int] = None
    shares_authorized_preferred: Optional[str] = None
    shares_issued_preferred: Optional[int] = None
    notes: Optional[str] = None


class ClientPaymentCard(BaseModel):
    id: Optional[int] = None
    last_4_digits: Optional[str] = None
    expired_at: Optional[datetime.datetime] = None
    cid: Optional[str] = None
    linked_to: Optional[str] = None
    card_holder: Optional[str] = None
    debit_card: Optional[str] = None
    exp: Optional[str] = None

    _date_validator = field_validator(
        *['expired_at'],
        mode='before'
    )(json_utils.date_validator)


class ClientPaymentService(BaseModel):
    id: Optional[int] = None
    payment_system: Optional[str] = None
    date_opened: Optional[datetime.datetime] = None
    opened_by: Optional[str] = None
    email_connected: Optional[str] = None
    responsible_person: Optional[str] = None
    login_pass: Optional[str] = None
    note: Optional[str] = None

    _date_validator = field_validator(
        *['date_opened'],
        mode='before'
    )(json_utils.date_validator)


class ClientSource(BaseModel):
    id: Optional[str] = None
    title: Optional[str] = None


class ClientContact(BaseModel):
    id: Optional[int] = None
    client_person_id: Optional[str] = None
    position: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    pcm: Optional[str] = None
    note: Optional[str] = None
    person: Optional[client_persons.PersonCreate] = None


class InternalDataSource(BaseModel):
    type: Optional[str] = None
    id: Optional[str] = None


class ClientCommon(ClientBase):
    manager: Optional[ClientManager] = None
    source: Optional[ClientSource] = None
    addresses: Optional[List[ClientAddress]] = None
    authorized_signers: Optional[List[authorized_signers_api.AuthorizedSignerUpdate]] = None
    bank_accounts: Optional[List[bank_accounts_api.BankAccountUpdate]] = None
    contacts: Optional[List[ClientContact]] = None
    payment_cards: Optional[List[ClientPaymentCard]] = None
    payment_services: Optional[List[ClientPaymentService]] = None
    primary_registration: Optional[ClientRegistration] = None
    registrations: Optional[List[ClientRegistration]] = None
    secondary_registrations: Optional[List[ClientRegistration]] = None
    share_classes: Optional[List[ClientShareClass]] = None
    shareholders: Optional[List[ClientShareholder]] = None
    services: Optional[List[ClientService]] = None
    tasks: Optional[List[ClientTask]] = None
    tax_reporting: Optional[List[ClientTaxReporting]] = None

    # Internal
    internal_data_source: Optional[InternalDataSource] = None


class ClientCreate(ClientCommon):
    name: str


class ClientUpdate(ClientCommon):
    name: Optional[str] = None


class ClientApprove(BaseModel):
    note: Optional[str] = None


def get_router(prefix='/'):
    router = APIRouter(
        prefix=os.path.join(prefix, 'clients'),
        dependencies=[Depends(auth_utils.basic_access)]
    )

    router.add_api_route("", list_clients, methods=['GET'], name='List clients')
    router.add_api_route("/tabs_available", list_client_tabs, methods=['GET'], name='List clients')
    router.add_api_route("/validate", validate_client, methods=['POST'], name='Validate client')
    router.add_api_route("", create_client, methods=['POST'], name='Create client')
    router.add_api_route("/{client_id}", get_client, methods=['GET'], name='Get client')
    router.add_api_route("/{client_id}", update_client, methods=['PUT'], name='Update client')
    router.add_api_route("/{client_id}/validate", validate_client, methods=['POST'], name='Validate client')
    router.add_api_route("/{client_id}/approve", approve_client, methods=['PUT'], name='Approve client')
    router.add_api_route("/{client_id}/cancel_changes", cancel_changes, methods=['PUT'], name='Cancel changes')
    router.add_api_route("/{client_id}/approved_data", get_approved_client_data, methods=['GET'], name='Get approved client data')
    router.add_api_route("/{client_id}", delete_client, methods=['DELETE'], name='Delete client')

    client_prefix = '/{client_id}'
    router.include_router(files.get_router(prefix='/'), prefix=client_prefix, tags=['Files'])
    router.include_router(contact_api.get_router(prefix='/'), prefix=client_prefix, tags=['Contacts'])
    router.include_router(client_addresses.get_router(prefix='/'), prefix=client_prefix, tags=['Client Addresses'])
    router.include_router(client_registrations.get_router(prefix='/'), prefix=client_prefix, tags=['Registrations'])
    router.include_router(client_persons.get_router(prefix='/'), prefix=client_prefix, tags=['Persons'])
    router.include_router(authorized_signers_api.get_router(prefix='/'), prefix=client_prefix, tags=['Authorized Signers'])
    router.include_router(bank_accounts_api.get_router(prefix='/'), prefix=client_prefix, tags=['Bank Accounts'])
    router.include_router(client_tasks.get_router(prefix='/'), prefix=client_prefix, tags=['Client Tasks'])
    router.include_router(client_services.get_router(prefix='/'), prefix=client_prefix, tags=['Client Services'])
    for r in changes_pending.get_routers(prefix='/'):
        router.include_router(r, prefix=client_prefix, tags=['Pending Changes'])

    router.include_router(search.get_router(prefix='/'), prefix=client_prefix, tags=['Client search'])
    router.include_router(questions.get_router(prefix='/'), prefix=client_prefix, tags=['Questions'])

    return router


async def list_clients(
    limit: int = 25,
    page: int = 1,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    internal_draft_flag: bool = None,
    # session: AsyncSession = Depends(get_session)
):
    clients, count = await db_api.list_clients(
        limit=limit, page=page, order=order, desc=desc, q=q, internal_draft_flag=internal_draft_flag
    )
    clients_dict = [c.to_dict() for c in clients]
    for client in clients_dict:
        if 'addresses' in client:
            for addr in client['addresses']:
                if 'address' in addr and addr['address']:
                    addr['address']['full_title'] = addr['address'].get('full_address')

    return {'items': clients_dict, 'count': count, 'limit': limit, 'page': page}


async def list_client_tabs():
    return clients.list_client_tabs()


async def get_client(client_id: str):
    _, client_dict = await clients.get_client_data(client_id)

    for addr in client_dict.get('addresses', []):
        if 'address' in addr and addr['address']:
            addr['address']['full_title'] = addr['address'].get('full_address')

    return client_dict


async def validate_client(client: dict, client_id=None):
    # Take schema
    schema = schemas.api_client_schema
    # Validate client data
    result = {
        'valid': True,
        'error': None,
        'message': None,
        'details': []
    }

    try:
        client_data = ClientCreate.model_validate(client)
    except ValidationError as e:
        result = json_utils.parse_pydantic_error(e)
        return result

    # Validate without raising an exception
    validation_error = json_utils.validate_schema(client, schema, raise_exception=False)

    if validation_error:
        # Use the parse_pydantic_error function to get a comprehensive error message
        result = json_utils.parse_validation_error(validation_error)

    return result


async def create_client(client: ClientCreate):
    # check for duplicate name
    if await db_api.get_client_by(name=client.name, notfoundok=True):
        raise HTTPException(status_code=409, detail="Client with name %s already exists" % client.name)

    db_client = await db_api.create_client(client.model_dump(exclude_unset=True))
    _, client_data_dict = await clients.get_client_data(db_client.id)
    return ORJSONResponse(content=client_data_dict)


async def update_client(client_id: str, client: ClientUpdate):
    client_dict = client.model_dump(exclude_unset=True)

    # Set draft flag if client is not in draft
    if not client_dict.get('internal_draft_flag', False):
        client_dict['internal_draft_flag'] = True
    updated_client = await clients.update_client_data(client_id, client_dict)

    for addr in updated_client.get('addresses', []):
        if 'address' in addr and addr['address']:
            addr['address']['full_title'] = addr['address'].get('full_address')

    return updated_client


async def approve_client(client_id: str, approve_data: Optional[ClientApprove] = None):
    manager: models.Manager = ctx.current_manager()
    return await clients.approve_client_data(client_id, manager.title, approve_data.note if approve_data else None)


async def get_approved_client_data(client_id: str):
    return await clients.get_last_approved_client_data(client_id)


async def cancel_changes(client_id: str):
    async with base.session_context():
        last_data = await clients.get_last_approved_client_data(client_id)
        last_data['internal_draft_flag'] = False
        updated_client = await clients.update_client_data(client_id, last_data)

    return updated_client


async def delete_client(client_id: str):
    db_client = await db_api.get_client_by_id(client_id)

    async with base.session_context():
        await db_api.delete_client(client_id)
        # Delete client connected data
        await db_api.delete_client_addresses(client_id=client_id)
        await db_api.delete_client_bank_accounts(client_id=client_id)
        await db_api.delete_client_contacts(client_id=client_id)
        await db_api.delete_client_files(client_id=client_id)
        await db_api.delete_debit_cards(client_id=client_id)
        await db_api.delete_client_payment_systems(client_id=client_id)
        await db_api.delete_client_registrations(client_id=client_id)
        await db_api.delete_client_services(client_id=client_id)
        await db_api.delete_client_share_classes(client_id=client_id)
        await db_api.delete_client_shareholders(client_id=client_id)
        await db_api.delete_client_tasks(client_id=client_id)
        await db_api.delete_client_tax_reportings(client_id=client_id)

    return Response(status_code=204)
